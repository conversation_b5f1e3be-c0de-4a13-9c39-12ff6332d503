/**
 * FE ASH 统一接口类型定义
 */

// 基础请求体
export interface HairuoRequestBody {
    userid: string;
    params: any;
}

// 响应状态枚举
export enum HairuoResponseStatus {
    SUCCESS = 0,
    PARTIAL_FAILURE = 1,
    FAILURE = 2,
}

// 错误信息接口
export interface HairuoResponseError {
    code: number;
    message: string;
}

// 统一响应体
export interface HairuoResponseBody<T = any> {
    status: HairuoResponseStatus;
    data: T;
    errors: HairuoResponseError[];
}

// 通用的错误码类型
export interface ErrorCode {
    code: number;
    message: string;
}

// 错误码定义结构
export type ErrorCodeMap = Record<string, ErrorCode>;
