/**
 * 统一响应处理工具函数
 */

import {
    HairuoResponseBody,
    HairuoResponseStatus,
    HairuoResponseError,
} from '../types/base';

/**
 * 创建成功响应
 */
export function createSuccessResponse<T>(data: T): HairuoResponseBody<T> {
    return {
        status: HairuoResponseStatus.SUCCESS,
        data,
        errors: [],
    };
}

/**
 * 创建失败响应
 */
export function createFailureResponse(
    errors: HairuoResponseError[],
    data: any = null
): HairuoResponseBody {
    return {
        status: HairuoResponseStatus.FAILURE,
        data,
        errors,
    };
}

/**
 * 创建部分失败响应
 */
export function createPartialFailureResponse<T>(
    data: T,
    errors: HairuoResponseError[]
): HairuoResponseBody<T> {
    return {
        status: HairuoResponseStatus.PARTIAL_FAILURE,
        data,
        errors,
    };
}

/**
 * 创建错误对象
 */
export function createError(code: number, message: string): HairuoResponseError {
    return {code, message};
}

/**
 * 通用错误码
 */
export const COMMON_ERRORS = {
    INVALID_PARAMS: {code: 400001, message: '参数错误'},
    UNAUTHORIZED: {code: 401001, message: '未授权访问'},
    FORBIDDEN: {code: 403001, message: '权限不足'},
    NOT_FOUND: {code: 404001, message: '资源不存在'},
    METHOD_NOT_ALLOWED: {code: 405001, message: '请求方法不支持'},
    INTERNAL_ERROR: {code: 500001, message: '服务器内部错误'},
    SERVICE_UNAVAILABLE: {code: 503001, message: '服务暂不可用'},
    TIMEOUT: {code: 504001, message: '请求超时'},
} as const;
