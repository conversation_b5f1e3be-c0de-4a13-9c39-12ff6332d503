/**
 * 请求验证工具函数
 */

import { HairuoRequestBody } from '../types/base';

/**
 * 验证 Hairuo 请求体格式
 */
export function validateHairuoRequest(body: any): body is HairuoRequestBody {
    return (
        body &&
        typeof body === 'object' &&
        typeof body.userid === 'string' &&
        body.userid.trim() !== '' &&
        body.params !== undefined
    );
}

/**
 * 验证 URL 格式
 */
export function isValidUrl(url: string): boolean {
    try {
        const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
        return urlRegex.test(url);
    } catch {
        return false;
    }
}

/**
 * 验证必需参数
 */
export function validateRequired<T>(
    params: T, 
    requiredFields: (keyof T)[]
): { isValid: boolean; missingFields: string[] } {
    const missingFields: string[] = [];
    
    for (const field of requiredFields) {
        if (params[field] === undefined || params[field] === null || params[field] === '') {
            missingFields.push(String(field));
        }
    }
    
    return {
        isValid: missingFields.length === 0,
        missingFields
    };
}

/**
 * 安全地提取数字参数
 */
export function safeParseInt(value: any, defaultValue: number = 0): number {
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * 安全地提取布尔参数
 */
export function safeParseBool(value: any, defaultValue: boolean = false): boolean {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
        const lower = value.toLowerCase();
        return lower === 'true' || lower === '1' || lower === 'yes';
    }
    return defaultValue;
}