Global:
  version: 2.0
  group_email: <EMAIL>

Default:
  profile: [build_ocr]

Profiles:
  - profile:
    name: build_ocr
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - python: 3.9.16
    build:
      command: bash bin/build_ocr.sh
    artifacts:
      release: true
  - profile:
    name: build_crawl
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - nodejs: 20.latest
        - pnpm: 8.3.1
    build:
      command: bash bin/build_crawl.sh
    artifacts:
      release: true
  - profile:
    name: build_ash_node
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - nodejs: 20.latest
        - pnpm: 8.3.1
    build:
      command: bash bin/build_ash_node.sh
    artifacts:
      release: true
  - profile:
    name: build_crawl4ai
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - python: 3.9.16
    build:
      command: bash bin/build_crawl4ai.sh
    artifacts:
      release: true
