FROM iregistry.baidu-int.com/baidu-base/python:3.12-centos7

ENV LANG=en_US.UTF-8

USER root

# 预装好常用的一些软件
RUN yum install -y lsof vim
RUN yum install -y mesa-libGL

# 创建1000号用户为work，创建目录、组、权限
RUN mkdir -p /home/<USER>
    && chown -R 1000:1000 /home/<USER>
    && sed -i '/^work:\|:x:1000:/d' /etc/group \
    && echo >> /etc/group \
    && echo 'work:x:1000:' >> /etc/group \
    && sed -i '/^work:\|:x:1000:/d' /etc/passwd  \
    && echo >> /etc/passwd \
    && echo 'work:x:1000:1000::/home/<USER>/bin/bash' >> /etc/passwd


# 创建应用根目录
RUN mkdir /home/<USER>/fe-ash-ocr

# 产物拷贝
COPY output/fe-ash-ocr /home/<USER>/fe-ash-ocr
COPY output/bin /home/<USER>/fe-ash-ocr/bin
COPY output/bin/pip.conf /etc/pip.conf
# ocr离线模型安装包
COPY output/fe-ash-ocr/scripts/ocr_model_setup.sh /home/<USER>/ocr_models/ocr_model_setup.sh

# 为 work 角色添加工作目录的执行权限
RUN mkdir -p /tmp/app/log
RUN chown work:work -R /home/<USER>/home/<USER>
RUN chown work:work -R /tmp && chmod +x -R /tmp

USER work

# 镜像里如果没有pip要安装
RUN /home/<USER>/fe-ash-ocr/bin/check_pip_installed.sh

# 创建 virtualenv 环境。venv不是必需的，请按自己的喜好以及实际需要选择。
RUN python3 -m pip install --upgrade pip \
    && python3 -m pip install virtualenv \
    && python3 -m virtualenv /home/<USER>/fe-ash-ocr/venv

# 激活 virtualenv 并安装依赖
RUN /bin/bash -c "source /home/<USER>/fe-ash-ocr/venv/bin/activate \
    && python3 -m pip install --upgrade pip \
    && python3 -m pip install -r /home/<USER>/fe-ash-ocr/requirements.txt"

# 设置全局变量，记录 virtualenv 路径
ENV INSURANCE_BOT_VENV_BASE_PATH="/home/<USER>/fe-ash-ocr/venv"

ENV EM_HEALTH_URI="/health"
ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8

# 设置工作目录
WORKDIR /home/<USER>/fe-ash-ocr

# 无需配置 CMD，在 jarvis 平台创建/编辑应用时进行容器启动命令设置
