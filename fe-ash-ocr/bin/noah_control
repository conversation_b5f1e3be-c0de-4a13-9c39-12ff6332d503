#!/bin/bash
APP_HOME=/home/<USER>/fe-ash-ocr
HEALTH_URL=/health

PORT=5858
if [[ -n "$EM_PORT" ]]; then
  PORT=$EM_PORT
  echo '[INFO] port: '$PORT
fi

VENV_PATH=/home/<USER>/fe-ash-ocr/venv/bin/activate

# 挂载日志
APP_SHARED_LOG_HOME=/tmp/app/log
mkdir -p $APP_SHARED_LOG_HOME

function start() {
  export TZ='Asia/Shanghai'
  ln -snf $APP_SHARED_LOG_HOME $APP_HOME/log
  echo $$ > $APP_SHARED_LOG_HOME/instance.pid
  cd $APP_HOME/task

  source $VENV_PATH
  python3 -m uvicorn main:app --host 0.0.0.0 --port $PORT --workers 6
  # exec uvicorn main:app --host 0.0.0.0 --port $PORT --workers 6
}

function status() {
  echo '[info] checking health: '${HEALTH_URL}
  curl -v --connect-timeout 3 --max-time 60 http://localhost:${PORT}${HEALTH_URL}
  if [[ $? -ne 0 ]]; then
    echo '[FAILED]'
    exit 6
  fi
  echo '[DONE]'
}

function stop() {
  kill -9 $(lsof -ti:$PORT)
  echo 'stop success'
}

action=$1
shift
case $action in
  start)
    echo "Starting..."
    start $@
    ;;
  stop)
    echo "Stopping..."
    stop $@
    ;;
  status)
    echo "Getting status..."
    status $@
    ;;
  *)
    echo "Usage: $0 {start|stop|status} [args]"
    exit 2
esac
