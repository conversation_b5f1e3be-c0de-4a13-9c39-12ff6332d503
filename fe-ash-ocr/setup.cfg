# Here's a link about setup.cfg
# https://setuptools.readthedocs.io/en/latest/setuptools.html#configuring-setup-using-setup-cfg-files
[metadata]
# Project name, the project name will be used while publishing and installing
name = fe-ash-ocr
# Author's name and email address
author = zhangbo35
author_email = <EMAIL>
# Project version, versions only above than 1.0 will assumed as a released version.
# When modifying project version to above than 1.0, here's the rules should be followed.
# http://wiki.baidu.com/pages/viewpage.action?pageId=469686381
version = 0.1.0
# A brief introduction about the project, ANY NON-ENGLISH CHARACTER IS NOT SUPPORTED!
description = project descriptions here
# A longer version of introduction abouth the project, you can also include readme, change log, etc. .md or rst file is recommended.
long_description = file: README.md, CHANGELOG.md
long_description_content_type = text/markdown
# Main page of the project, usually the project's icode page, you can set to its wiki or other documents url instead.
home_page = https://console.cloud.baidu-int.com/devops/icode/repos/baidu/fc-fe/fe-ash/tree/master/fe-ash-ocr
# License, you can ignore this if the project is not going to open source to the public.
license = MIT
# Project type, you can ignore this if the project is not going to open source to the public.
# Choose the right field to fulfill from PyPI's official list.
# https://pypi.org/pypi?%3Aaction=list_classifiers
classifier =
    Private :: Do Not Upload
    Programming Language :: Python
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.9
# keywords, used for indexing, easier to search for other users if they are interested of your project.
#keywords =
#    baidu
#    demo

[options]
# Package name. find means search automatically, you also can have detailed configuration in options.packages.find
packages = find:
# Dependency management, all project's dependency is needed here.
# Every single line for a specified dependency, only the dependency is need, you don't have to consider the hierarchy dependency
# Versions here should be as abstract as possible, usually only specific a version range including minimum and maximum version.
# install_requires =
#     six >= 1.10

# Test dependencies, all dependencies for tests here. The format is align to install_requires.
# You can use the internal unittest, or the simplier framework such as pytest or nose.
# python3 has a mock library with itself, but it's not exist in python 2, add as you need.
#tests_require =
#    pytest
#    mock

# add all data files controled by git
include_package_data = True
# You can run zip source code for plain python project
zip_safe = False

[sdist]
dist_dir = output/dist

[bdist_wheel]
# set universal=1 if this project can both run in python 2 or 3 environment.
#universal=1
dist_dir = output/dist

[easy_install]
# using baidu's official pip source
index_url = http://pip.baidu.com/root/baidu/+simple/