# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ocr服务入口
"""
import http
import os
import sys

# 在服务自身模块加载前要先加入path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import uvicorn
import traceback
from fastapi import FastAPI, Request
from starlette.middleware.cors import CORSMiddleware
from task.common.model import Result
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from task.common.exception import CustomException
from task.common.logger import logger
from task.ocr import ocrrouter
port = 8067
if os.environ.get("EM_PORT") is not None:
    port = int(os.environ.get("EM_PORT"))


def start_event():
    """ 创建一个会话管理器
    """
    print("The server startup.......")


def shutdown_event():
    """ 关闭事件
    """
    print("The server shutdown.......")


def init_router(fast_api_app):
    """
    初始化路由
    @param fast_api_app: fastapi应用
    """
    fast_api_app.include_router(ocrrouter.router, prefix="/ash-api")


def create_app():
    """ 创建fastapi应用
    """
    fast_api_app = FastAPI(on_startup=[start_event], on_shutdown=[shutdown_event])
    fast_api_app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_methods=["*"], allow_credentials=True,
                                allow_headers=["*"])
    init_router(fast_api_app)
    return fast_api_app


app = create_app()


@app.exception_handler(CustomException)
async def custom_exception_handler(request: Request, exc: CustomException):
    """ 自定义异常处理
    """
    logger.get_logger.error(traceback.format_exc())
    result = Result(code=exc.status_code, detail=exc.detail, status="failed")
    return JSONResponse(jsonable_encoder(result), status_code=http.HTTPStatus.OK)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """ 参数校验异常处理
    """
    logger.get_logger.error(traceback.format_exc())
    err = exc.errors()[0]
    result = Result(code=400, detail=str(err), status="failed")
    return JSONResponse(jsonable_encoder(result), status_code=http.HTTPStatus.OK)


@app.exception_handler(Exception)
async def default_exception_handler(request: Request, exc: Exception):
    """ 全局异常处理
    """
    logger.get_logger.error(traceback.format_exc())
    result = Result(code=500, detail=str(exc), status="failed")
    return JSONResponse(jsonable_encoder(result), status_code=http.HTTPStatus.OK)


@app.get("/health", response_model=Result)
async def health():
    """ 根路径
    @return: {"Hello World!"}
    """
    return Result(data="Hello World!")


if __name__ == '__main__':
    uvicorn.run(
        app='main:app',
        host='0.0.0.0',
        port=port,
        reload=False,
        workers=10
    )