"""
base64_to_image
"""
import base64
import os
import uuid
def base64_to_image(base64_string):
    """
    @param base64_string:
    @return:
    """
    parent_dir = os.getcwd() + "/ocr_imgs/"
    if not os.path.exists(parent_dir):
        os.makedirs(parent_dir)
    output_file_path = os.path.join(parent_dir, str(uuid.uuid4()).replace("-", "") + determine_image_format(base64_string))
    # 解码 Base64 字符串
    image_data = base64.b64decode(base64_string.split(',', 1)[1])
    # 将二进制数据写入文件
    with open(output_file_path, 'wb') as output_file:
        output_file.write(image_data)
    return output_file_path

def determine_image_format(base64_string):
    """
    @param base64_string:
    @return:
    """
    # 如果包含数据 URI 前缀，则移除它
    if base64_string.startswith('data:image'):
        header, base64_data = base64_string.split(',', 1)
        if 'jpeg' in header:
            return '.jpeg'
        elif 'png' in header:
            return '.png'
    else:
        base64_data = base64_string
    # 解码 Base64 字符串
    image_data = base64.b64decode(base64_data)
    # 检查文件头
    if image_data.startswith(b'\xFF\xD8'):
        return '.jpeg'
    elif image_data.startswith(b'\x89PNG'):
        return '.png'
    raise ValueError('Unsupported image format')