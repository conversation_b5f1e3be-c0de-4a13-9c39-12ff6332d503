"""
import pymysql
"""
import os

import pymysql
import socket
from task.common import dbconfig
from task.common.logger import logger
import traceback

def get_connection():
    """
    获取数据库连接
    """
    connection = None
    try:
        if os.getenv('EM_ENV_TYPE') == 'ONLINE':
            connection = pymysql.connect(
                host=socket.gethostbyname(dbconfig.online['host']),  # 数据库主机地址，本地一般是'localhost'
                user=dbconfig.online['user'],  # 数据库用户名
                password=dbconfig.online['password'],  # 你的数据库密码
                database=dbconfig.online['database'],  # 要连接的数据库名称
                port=dbconfig.online['port']
            )
        elif os.getenv('EM_ENV_TYPE') == 'OFFLINE':
            connection = pymysql.connect(
                host=socket.gethostbyname(dbconfig.offline['host']),  # 数据库主机地址，本地一般是'localhost'
                user=dbconfig.offline['user'],  # 数据库用户名
                password=dbconfig.offline['password'],  # 你的数据库密码
                database=dbconfig.offline['database'],  # 要连接的数据库名称
                port=dbconfig.offline['port']
            )
        else:
            connection = pymysql.connect(
                host=dbconfig.local['host'],  # 数据库主机地址，本地一般是'localhost'
                user=dbconfig.local['user'],  # 数据库用户名
                password=dbconfig.local['password'],  # 你的数据库密码
                database=dbconfig.local['database'],  # 要连接的数据库名称
                port=dbconfig.local['port']
            )
        if connection is not None:
            return connection
        else:
            raise Exception("Connection is None.")
    except Exception as e:
        error_message = "get Connection failed, {}\n{}".format(e, traceback.format_exc())
        logger.get_logger.error(error_message)



def select_all_from_cc_genie_ocr_cache(url):
    """
    从asr_origin_video_result表中查询指定id的记录
    """
    connection = get_connection()
    cursor = connection.cursor()
    select_sql = ("select text from cc_genie_ocr_cache where url = %s")
    cursor.execute(select_sql, (url,))
    row = cursor.fetchone()
    cursor.close()
    connection.close()
    return row[0]

def url_is_exist(url):
    """
    判断指定url是否存在于cc_genie_ocr_cache表中
    """
    connection = get_connection()
    cursor = connection.cursor()
    sql = ("select count(1) from cc_genie_ocr_cache where url = %s")
    cursor.execute(sql, (url))
    result = cursor.fetchone()  # 获取查询结果
    count = 0
    if result is not None:
        count = result[0]  # 提取count(1)的值
        logger.get_logger.info("Count result: {}".format(count))
    else:
        logger.get_logger.info("No result found, count is 0")
    cursor.close()
    connection.close()
    return count > 0

def insert_into_cc_genie_ocr_cache(url, text):
    """
    插入一条新的记录到asr_origin_video_result表中
    """
    connection = get_connection()
    cursor = connection.cursor()
    insert_sql = "INSERT INTO cc_genie_ocr_cache (url, add_time, text) VALUES (%s, NOW(), %s)"
    cursor.execute(insert_sql, (url, text))
    connection.commit()
    cursor.close()