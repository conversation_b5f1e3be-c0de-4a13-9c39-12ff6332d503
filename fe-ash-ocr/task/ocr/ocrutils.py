""" ocr识别相关工具类
"""
import json
import time

from pymysql import IntegrityError
from task.common.logger import logger
import requests
from paddleocr import PaddleOCR
import uuid
import os
# todo from task.utils import sqlutils
from task.utils import commonutils

def get_text_from_image_by_paddle(url):
    """
    从图片中识别出文字
    """

    result = []
    # 先查一下有没有理解过
    # todo if (sqlutils.url_is_exist(url) is True):
    # todo    return sqlutils.select_all_from_cc_genie_ocr_cache(url)
    filename = download_file(url)
    try:
        """ 将给定的URL中的图片下载到本地并进行ocr识别
        """
        if filename is None:
            raise Exception('download file failed')
        begin = time.time()
        # need to run only once to download and load model into memory
        paddleOcr = PaddleOCR(use_angle_cls=True, lang="ch")
        output = paddleOcr.ocr(filename, cls=True)
        end = time.time()
        for idx in range(len(output)):
            res = output[idx]
            if res is None:
                break
            for line in res:
                result.append({'coordinate': line[0], 'text': line[1][0]})
        logger.get_logger.info('input: {}, time cost: {} s, output: {}'.format(url, end - begin, result))
        if len(result) == 0:
            return None
        else:
            text = json.dumps(result, ensure_ascii=False)
            # sqlutils.insert_into_cc_genie_ocr_cache(url, text)
            return text
    except IntegrityError as e:
        # 并发情况下走到这里，直接补获异常返回
        if e.args[0] == 1062 and 'Duplicate entry' in e.args[1]:
            # todo return sqlutils.select_all_from_cc_genie_ocr_cache(url)
            return 'Duplicate entry'
    finally:
        if os.path.exists(filename):
            os.remove(filename)

def get_text_from_image_base64(base64):
    """
    base 64转图片识别
    """
    filename = None
    if base64 is None:
        raise Exception('base64 is None')
    try:
        result = []
        filename = commonutils.base64_to_image(base64)
        """ 将给定的URL中的图片下载到本地并进行ocr识别
        """
        begin = time.time()
        # need to run only once to download and load model into memory
        paddleOcr = PaddleOCR(use_angle_cls=True, lang="ch")
        output = paddleOcr.ocr(filename, cls=True)
        end = time.time()
        for idx in range(len(output)):
            res = output[idx]
            if res is None:
                break
            for line in res:
                result.append({'coordinate': line[0], 'text': line[1][0]})
        logger.get_logger.info('input: {}, time cost: {} s, output: {}'.format(base64, end - begin, result))
        if len(result) == 0:
            return None
        else:
            text = json.dumps(result, ensure_ascii=False)
            return text
    except Exception as e:
        logger.get_logger.error("get_text_from_image_base64 failed", e)
        raise e
    finally:
        if os.path.exists(filename):
            os.remove(filename)

def download_file(url):
    """ 将给定的URL中的文件下载到文本
    """
    # 使用rfind()方法查找最后一个斜杠的位置
    filename = os.path.basename(url)
    file_suffix = os.path.splitext(filename)[1]
    file_preffix = str(uuid.uuid4())
    # 发送HTTP GET请求获取文件内容
    response = requests.get(url)
    # 检查请求是否成功
    if response.status_code == 200:
        parentDir = os.getcwd() + "/ocr_imgs/"
        if not os.path.exists(parentDir):
            os.makedirs(parentDir)
        finalFile = os.path.join(parentDir, file_preffix + file_suffix)
        # 创建不存在的目录
        with open(finalFile, 'wb') as file:
            file.write(response.content)
        return finalFile
    else:
        logger.get_logger.warn('download file failed {}'.format(url))