""" ocr识别相关接口
"""
from fastapi import APIRouter, Depends

from task.common import dependencies
from task.common.model import Result
from pydantic import BaseModel
from task.common.logger import logger
from task.ocr import ocrutils

router = APIRouter(
    prefix="/ocr",
    tags=["ocr"],
    dependencies=[Depends(dependencies.get_token_header)],
    responses={404: {"description": "Not found"}}
)
class Item(BaseModel):
    """
    请求参数
    Attributes:
        url: str - 请求的URL
    """
    url: str

class ImgBASE64(BaseModel):
    """
    请求参数
    Attributes:
        base64: str - 请求的base64
    """
    base64: str
@router.post("/getTextFromImageByPaddle")
async def get_text_from_image_by_paddle(item: Item):
    """
    根据图片地址获取图片中的文字
    @param obj: 请求参数
    @return: {"status": "success"}
    """
    url = item.url
    logger.get_logger.info('get_text_from_image_by_paddle, input:{}'.format(url))
    result = ocrutils.get_text_from_image_by_paddle(url)
    if result is None:
        return Result(400, 'error', '未识别到文字', 'None')
    else:
        return Result(200, 'success', '识别成功', result)


@router.post("/getTextFromImageBase64")
async def get_text_from_image_base64(item: ImgBASE64):
    """
    @param item: ITEM_BASE64
    @return: {"status": "success"}
    """
    logger.get_logger.info('get_text_from_image_by_paddle, base64 input:{}'.format(item.base64))
    result = ocrutils.get_text_from_image_base64(item.base64)
    if result is None:
        return Result(400, 'error', '未识别到文字', 'None')
    else:
        return Result(200, 'success', '识别成功', result)