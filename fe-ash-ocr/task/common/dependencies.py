# !/usr/bin/env python3
# -*- coding: utf-8 -*-
""" 公共依赖函数
"""
from typing import Annotated

from fastapi import Header, HTTPException

VALID_TOKENS = {
    "smart-ad-builder", # 智能搭建
}

async def get_token_header(x_token: Annotated[str, Header()]):
    """
    校验请求头中的token
    @param x_token: 请求头中的token
    """
    if x_token not in VALID_TOKENS:
        raise HTTPException(status_code=400, detail="X-Token header invalid")