# !/usr/bin/env python3
# -*- coding: utf-8 -*-
""" 请求对象
"""
from pydantic import BaseModel
from typing import Union, Any


class ImageItem(BaseModel):
    """
    ImageAndAudioItem对象
    imagePath: 图片路径
    duration: 图片持续时间
    order: 图片顺序，默认为0
    """
    imagePath: str
    duration: int
    order: Union[int, None] = 0


class Audio(BaseModel):
    """
    Audio对象
    DubbingPath: 配音文件路径
    """
    dubbingPath: str


class ImageAudioSubtitles(BaseModel):
    """
    ImageAndAudio
    images: 图片列表
    audio: 音频
    subtitlesPath: 字幕文件路径
    outputPath: 输出视频路径
    """
    images: list[ImageItem]
    audio: Audio
    subtitlesPath: str
    outputPath: str


class ImageAppendVideo(BaseModel):
    """
    ImageAppendVideo对象
    title: 标题
    image: 吸睛图片全路径
    videoPath: 视频全路径
    outputPath: 输出生成视频路径
    """
    title: Union[str, None] = None
    imagePath: str
    videoPath: str
    outputPath: str


class VideoConversion(BaseModel):
    """
    VideoConversion对象
    videoPath: 视频全路径
    outputPath: 输出生成视频路径
    """
    videoPath: str
    outputPath: str


class VideoMerge(BaseModel):
    """
    VideoMerge对象
    DigitalHumanVideo: 视频全路径
    backgroundVideo: 背景视频全路径
    outputPath: 输出生成视频路径
    """
    digitalHumanVideo: str
    backgroundVideo: str
    outputPath: str


class OverlayImage(BaseModel):
    """
    OverlayImage对象
    topImagePath: 头图路径
    bottomImagePath: 底图路径
    outputPath: 输出生成图片路径
    """
    topImagePath: str
    bottomImagePath: str
    outputPath: str


class ImageResize(BaseModel):
    """
    ImageResize对象
    imagePath: 原图绝对路径
    targetWidth: 目标宽度
    targetHeight: 目标高度
    outputPath: 输出生成图片路径
    """
    imagePath: str
    targetWidth: int
    targetHeight: int
    outputPath: str


class Result(BaseModel):

    """
    Result对象
    code: 状态码
    status: 状态
    detail: 状态描述
    data: 数据
    """
    code: int = 200
    status: str = "success"
    detail: str = "success"
    data: Any = None

    def __init__(self, code: int = 200, status: str = None, detail: str = "success", data: Any = None):
        super().__init__(code=code, detail=detail)
        self.code = code or self.code
        self.status = status or self.status
        self.detail = detail or self.detail
        self.data = data or self.data