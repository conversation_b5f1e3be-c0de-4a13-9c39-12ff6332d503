# !/usr/bin/env python3
# -*- coding: utf-8 -*-
""" 日志封装
"""

from loguru import logger as lg
import os


def singleton(cls):
    """
    实现单例模式的装饰器。

    Args:
        cls: 需要实现单例的类。

    Returns:
        返回一个内部函数get_instance，该函数用于获取类的单例对象。

    """
    instances = {}

    def get_instance(*args, **kwargs):
        """
        获取指定类的单例对象。
        Args:
            *args: 传入给类构造函数的任意位置参数。
            **kwargs: 传入给类构造函数的任意关键字参数。
        Returns:
            指定类的单例对象。
        """
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return get_instance


@singleton
class AppLogger:
    """ 应用日志记录器类
    """

    def __init__(self):
        """
        初始化函数，用于创建对象时初始化实例变量。
        Args:
            无参数。
        Returns:
            无返回值。
        """
        self.app_logger = lg

    def set_logger(self, filename, filter_type=None, level='DEBUG'):
        """
        :param filename: 日志文件名
        :param filter_type: 日志过滤，如：将日志级别为ERROR的单独记录到一个文件中
        :param level: 日志级别设置
        :return:
        """
        dic = dict(
            sink=self.get_log_path(filename),
            rotation='500 MB',
            retention='7 days',
            format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:"
                   "<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            encoding='utf-8',
            level=level,
            enqueue=True,
        )
        if filter_type:
            dic["filter"] = lambda x: filter_type in str(x['level']).upper()
        self.app_logger.add(**dic)
        return self.app_logger

    @property
    def get_logger(self):
        """
        获取日志记录器对象。
        Args:
            无参数。
        Returns:
            logging.Logger: 日志记录器对象。
        """
        return self.app_logger

    @staticmethod
    def get_log_path(filename):
        """
        获取日志文件的绝对路径。
        Args:
            filename (str): 日志文件名。
        Returns:
            str: 日志文件的绝对路径。
        """
        log_path = os.path.join(os.path.curdir, filename)
        return log_path

    def trace(self, msg):
        """
        向应用日志记录器中写入跟踪级别的日志信息。
        Args:
            msg (str): 日志信息字符串。
        Returns:
            None
        """
        self.app_logger.trace(msg)

    def debug(self, msg):
        """
        向应用日志记录器中写入调试级别的日志信息。
        Args:
            msg (str): 日志信息字符串。
        Returns:
            None
        """
        self.app_logger.debug(msg)

    def info(self, msg):
        """
        记录info级别的日志信息。
        Args:
            msg (str): 日志信息字符串。
        Returns:
            None
        """
        self.get_logger.info(msg)

    def success(self, msg):
        """
        记录成功级别的日志信息。
        Args:
            msg (str): 成功日志信息字符串。
        Returns:
            None
        """
        self.app_logger.success(msg)

    def warning(self, msg):
        """
        记录警告级别的日志信息。
        Args:
            msg (str): 警告日志信息字符串。
        Returns:
            None
        """
        self.app_logger.warning(msg)

    def error(self, msg):
        """
        记录错误级别的日志信息。
        Args:
            msg (str): 错误日志信息字符串。
        Returns:
            None
        """
        self.app_logger.error(msg)

    def critical(self, msg):
        """
        记录严重错误级别的日志信息。
        Args:
            msg (str): 严重错误日志信息字符串。
        Returns:
            None
        """
        self.app_logger.critical(msg)


logger = AppLogger()
# todo dir = '/home/<USER>/fe-ash-ocr/log'
dir = '../log'
logger.set_logger(dir + '/error.log', filter_type='ERROR')
logger.set_logger(dir + '/app.log', level='INFO')