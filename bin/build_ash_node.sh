#!/bin/sh
export LANG=zh_CN.UTF-8
# jarvis app name
JARVIS_APP_NAME='fe-ash-node'
# code build folder(eg: dist or build)
BUILD_FOLDER='dist'
# The absolute path of your code
BASEDIR=$(pwd)
# output folder(eg: output/node-for-jarvis)
OUTPUT=output/${JARVIS_APP_NAME}
# custom node version for agile build
NPM_REGITRY="http://registry.npm.baidu-int.com"

function procedure_check() {
    [[ "$?" != "0" ]] && exit $?
    return 0
}

# 准备build node 环境，不需要修改
function prepare_build_env() {
    # node_modules should be another repo, build more quickly
    # Add node and node_modules env
    # 编译集群提供了node环境，直接使用即可（其他版本：http://wiki.baidu.com/pages/viewpage.action?pageId=480000071）
    pnpm install --registry ${NPM_REGITRY} || exit 1
    echo '--install finish--'
}

function build() {
    echo '--build start--'
    ## tip: Do not support pass env params when build
    # 执行构建，具体构建方式根据自己项目自行编写
    pnpm build || exit 1; procedure_check
    # rename main.js to index.js, because pm2 will default start index.js
    echo '--build finish--'
    echo '--start pack tar--'
    mkdir -p ${OUTPUT}
    # get noahdes and bin from jarvis must not modify
    wget -O - --header "IREPO-TOKEN:4c969b9a-ee59-491a-9731-d1af56fe77e3" "http://irepo.baidu-int.com/rest/prod/v3/baidu/cpd-arch/jarvis-emc/releases/2020.08.133.1/files?m=baidu/cpd-arch/jarvis-emc"|tar -zxOf - './output/archer3-emcnode.tar.gz' | tar zx -C $OUTPUT || exit 3 ;[ -d "$OUTPUT/bin" ] && [ -d "$OUTPUT/noahdes" ] || exit 1
    # move build file to output
    # 将编译后的产出和服务运行需要的代码复制到output下,  node_modules（最好有pm2,否则每次npx启动pm2会下载pm2需要的包）和 package.json 必须要有
    cp -r ${BUILD_FOLDER}/* node_modules config package.json ${OUTPUT}/
    # if setenv then cp setenv to bin/
    # 自定义一些变量，目前node支持：
    # 1.健康检查path
    # 2.pm2 cluster模式instance数目
    # 3.如果在线上平台还要区分预上线，可自定义平台名称，然后在服务启动时会注入到node_env中
    cp -r bin/setenv.sh ${OUTPUT}/bin
    # pack the output as archer3 package
    tar -zcf ${OUTPUT}.tar.gz -C ${OUTPUT} .; procedure_check
    rm -rf ${OUTPUT}
    echo '--finish pack tar--'
}

# main
function main() {
    # echo node and pnpm version
    echo "node: $(node --version)"
    echo "pnpm: $(pnpm --version)"

    mkdir -p ${BASEDIR}/output
    cd ${BASEDIR}/fe-ash-node
    # Prepare node env and node_modules
    prepare_build_env; procedure_check
    # Build
    build;
    # move to output
    mv ${OUTPUT}.tar.gz ${BASEDIR}/output
    procedure_check
}

main "$@"
