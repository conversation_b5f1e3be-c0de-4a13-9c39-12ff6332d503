module.exports = {
    root: true,
    extends: [
        '@ecomfe/eslint-config',
        '@ecomfe/eslint-config/typescript',
    ],
    env: {
        node: true,
        es2022: true,
    },
    ignorePatterns: ['output/**/*', 'dist/**/*', '**/dist/**/*'],
    parserOptions: {
        ecmaVersion: 2022,
        sourceType: 'module',
        project: [
            './tsconfig.json',
            './fe-ash-crawl/tsconfig.json',
            './fe-ash-node/tsconfig.json',
            './fe-ash-shared/tsconfig.json',
        ],
    },
    rules: {
        '@typescript-eslint/init-declarations': 'warn',
    },
    overrides: [
        {
            files: ['fe-ash-crawl/**/*.ts'],
            parserOptions: {
                project: './fe-ash-crawl/tsconfig.json',
            },
        },
        {
            files: ['fe-ash-node/**/*.ts'],
            parserOptions: {
                project: './fe-ash-node/tsconfig.json',
            },
        },
        {
            files: ['fe-ash-shared/**/*.ts'],
            parserOptions: {
                project: './fe-ash-shared/tsconfig.json',
            },
            rules: {
                '@typescript-eslint/no-explicit-any': 'off',
                'max-len': ['error', {code: 120}],
            },
        },
        {
            files: ['**/vitest.config.ts'],
            parserOptions: {
                project: './tsconfig.json',
            },
            env: {
                node: true,
            },
        },
    ],
};
