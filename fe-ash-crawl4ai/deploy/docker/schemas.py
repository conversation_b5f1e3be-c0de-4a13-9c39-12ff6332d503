from typing import List, Optional, Dict
from enum import Enum
from pydantic import BaseModel, Field
from utils import FilterType

"""
Module docstring.
"""

class CrawlRequest(BaseModel):
    """
    CrawlRequest model.
    """
    urls: List[str] = Field(min_length=1, max_length=100)
    browser_config: Optional[Dict] = Field(default_factory=dict)
    crawler_config: Optional[Dict] = Field(default_factory=dict)


class MarkdownRequest(BaseModel):
    """
    Request body for the /md endpoint.
    """
    url: str = Field(..., description="Absolute http/https URL to fetch")
    f: FilterType = Field(FilterType.FIT, description="Content‑filter strategy: fit, raw, bm25, or llm")
    q: Optional[str] = Field(None, description="Query string used by BM25/LLM filters")
    c: Optional[str] = Field("0", description="Cache‑bust / revision counter")


class RawCode(BaseModel):
    """
    RawCode model.
    """
    code: str


class HTMLRequest(BaseModel):
    """
    HTMLRequest model.
    """
    url: str
    

class ScreenshotRequest(BaseModel):
    """
    ScreenshotRequest model.
    """
    url: str
    screenshot_wait_for: Optional[float] = 2
    output_path: Optional[str] = None


class PDFRequest(BaseModel):
    """
    PDFRequest model.
    """
    url: str
    output_path: Optional[str] = None


class JSEndpointRequest(BaseModel):
    """
    JSEndpointRequest model.
    """
    url: str
    scripts: List[str] = Field(
        ...,
        description="List of separated JavaScript snippets to execute"
    )