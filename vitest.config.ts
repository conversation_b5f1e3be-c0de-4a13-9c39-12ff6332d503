import {defineConfig} from 'vitest/config';
import path from 'path';

export default defineConfig({
    test: {
        globals: true,
        environment: 'node',
        exclude: ['**/node_modules/**', '**/dist/**', '**/output/**'],
        include: ['fe-ash-*/src/**/*.test.ts', 'fe-ash-*/src/**/*.spec.ts'],
    },
    resolve: {
        alias: {
            '@fe-ash-shared': path.resolve(__dirname, './fe-ash-shared/src'),
        },
    },
});
