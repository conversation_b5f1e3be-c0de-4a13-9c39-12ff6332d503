var http = require('http')

var options = {
  'method': 'POST',
  'hostname': 'localhost',
  'port': 8848,
  'path': '/ash-api/SmartAdBuilder/getFieldsTemplateByTrade',
  'headers': {
    'Content-Type': 'application/json'
  },
  'maxRedirects': 20
};

var req = http.request(options, function (res) {
  var chunks = [];

  res.on("data", function (chunk) {
    chunks.push(chunk);
  });

  res.on("end", function (chunk) {
    var body = Buffer.concat(chunks);
    console.log(body.toString());
  });

  res.on("error", function (error) {
    console.error(error);
  });
});

var postData = JSON.stringify({
  "userId": "630152",
  "params": JSON.stringify({
    "trade1": 2019,
  })
});

req.write(postData);

req.end();