import BaikalDB from '@baidu/node-baikaldb';
import config from 'config';
import {promisify} from 'util';

// @ts-ignore
const BaikalDBCls: any = BaikalDB && BaikalDB.default ? BaikalDB.default : BaikalDB;
const baikaldb = new BaikalDBCls({
    bns: config.get('baikaldb.bns'),
    port: config.get('baikaldb.port'),
    user: config.get('baikaldb.user'),
    password: config.get('baikaldb.password'),
    database: config.get('baikaldb.database'),
});

export interface SelectQuery {
    table: string;
    fields?: string[];
    conditions?: any[];
    groupBy?: string | string[];
    orderBy?: string;
    order?: string;
    limit?: number;
    offset?: number;
}

export interface UpdateQuery {
    table: string;
    fields?: Array<[string, string]>;
    conditions?: any[];
}

export interface DeleteQuery {
    table: string;
    conditions?: any[];
}

/**
 * 构建 SQL INSERT INTO 插入操作字符串
 * @param tableName 表格名字
 */
export function buildInsertQuery(tableName: string) {
    return `INSERT INTO ${tableName} SET ?`;
}

/**
 * 构建 SQL DELETE 删除操作字符串
 * @param tableName 表格名字
 * @param conditions where 条件
 */
export function buildDeleteQuery({table, conditions = []}: DeleteQuery) {
    let sql = `DELETE from ${table} `;

    // where 过滤
    if (conditions?.length) {
        sql += 'where ';
        for (const [i, c] of conditions.entries()) {
            if (c?.length !== 2) {
                continue;
            }
            sql += `${c[0]} = ${c[1]}`;
            if (i !== conditions.length - 1) {
                sql += ' and ';
            }
        }
    }

    return sql;
}

/**
 * 构建查询 sql，参数如下：
 *
 * table: string，表名
 * fields: string[]，查询字段数组，默认查询全部
 * conditions: [string, string][]，查询条件数组，每个元素为一个数组，第一个值为查询字段，第二个为查询字段值
 * groupBy: string | string[]，分组字段或字段数组
 * orderBy: string，排序字段
 * order: 排序方式，默认降序 `desc`，支持 `asc`
 * limit: 查询截断
 *
 * @param params 查询参数对象
 * @returns 拼接查询的sql字符串
 */
export function buildSelectQuery({
    table,
    fields = ['*'],
    conditions = [],
    groupBy = '',
    orderBy = '',
    order = 'desc',
    limit,
    offset,
}: SelectQuery) {
    let sql = `select ${fields.join(',')} from ${table} `;

    // where 过滤
    if (conditions?.length) {
        sql += 'where ';
        for (const [i, c] of conditions.entries()) {
            if (c?.length !== 2) {
                continue;
            }
            if (c[1] === 'NULL') {
                sql += `${c[0]} is ${c[1]}`;
            }
            else {
                sql += `${c[0]} = ${c[1]}`;
            }
            if (i !== conditions.length - 1) {
                sql += ' and ';
            }
        }
    }

    // group by 汇总
    if (groupBy?.length) {
        if (Array.isArray(groupBy)) {
            sql += ` group by ${groupBy.join(',')}`;
        }
        else {
            sql += ` group by ${groupBy}`;
        }
    }

    // order by 排序
    if (orderBy) {
        sql += ` order by ${orderBy} ${order}`;
    }

    // 限制条数
    if (limit) {
        sql += ` limit ${limit}`;
    }

    // 分页
    if (offset) {
        sql += ` offset ${offset}`;
    }

    return sql;
}

/**
 * 构建插入SQL语句，参数如下：
 *
 * table: string，表名
 * fields: [string , string][]，更新字段元组，每个元素为一个数组，第一个值为更新字段，第二个为字段值
 * conditions: [string, string][]，查询条件数组，每个元素为一个数组，第一个值为查询字段，第二个为查询字段值
 *
 * @param params 参数对象
 * @returns 插入SQL语句
 */
export function buildUpdateQuery({table, fields = [], conditions = []}: UpdateQuery) {
    let sql = `update ${table} set `;

    for (const [i, f] of fields.entries()) {
        if (f?.length !== 2) {
            continue;
        }
        sql += ` ${f[0]} = ${f[1]}`;
        if (i !== fields.length - 1) {
            sql += ',';
        }
    }

    // where 过滤
    if (conditions?.length) {
        sql += ' where ';
        for (const [i, c] of conditions.entries()) {
            if (c?.length !== 2) {
                continue;
            }
            sql += `${c[0]} = ${c[1]}`;
            if (i !== conditions.length - 1) {
                sql += ' and ';
            }
        }
    }

    return sql;
}

/**
 * 执行 DB 查询，返回 DB 查询结果
 * 如果出现异常，释放连接，抛出异常外层处理
 */
export async function dbQuery(sql: string, args?: any) {
    const connection = await baikaldb.getConnection();
    const query = promisify(connection.query.bind(connection));
    const timeout = 25000;

    const result = await (args ? query({sql, timeout, values: args}) : query({sql, timeout})).finally(() => {
        connection.release();
    });

    return result;
}
