import {describe, it, expect} from 'vitest';

describe('Environment Utils', () => {
    it('should export parsed command line arguments', async () => {
        const {args} = await import('../env');

        expect(args).toBeDefined();
        expect(args).toHaveProperty('address');
        expect(args).toHaveProperty('port');
        expect(args).toHaveProperty('logPath');
        expect(args).toHaveProperty('env');
    });
});