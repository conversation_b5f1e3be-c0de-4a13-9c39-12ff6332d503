import {describe, it, expect, vi} from 'vitest';
import {
    buildSelectQuery,
    buildUpdateQuery,
    buildDeleteQuery,
    buildInsertQuery,
} from '../db';

vi.mock('@baidu/node-baikaldb');
vi.mock('config', () => ({
    default: {
        get: vi.fn().mockReturnValue('mocked-value'),
    },
}));

describe('Database Utils', () => {
    describe('buildSelectQuery', () => {
        it('should build basic select query', () => {
            const sql = buildSelectQuery({
                table: 'users',
            });

            expect(sql).toBe('select * from users ');
        });

        it('should build query with specific fields', () => {
            const sql = buildSelectQuery({
                table: 'users',
                fields: ['id', 'name', 'email'],
            });

            expect(sql).toBe('select id,name,email from users ');
        });

        it('should build query with conditions', () => {
            const sql = buildSelectQuery({
                table: 'users',
                conditions: [['id', '123'], ['status', 'active']],
            });

            expect(sql).toContain('where id = 123 and status = active');
        });

        it('should build query with NULL condition', () => {
            const sql = buildSelectQuery({
                table: 'users',
                conditions: [['deleted_at', 'NULL']],
            });

            expect(sql).toContain('where deleted_at is NULL');
        });

        it('should build query with groupBy', () => {
            const sql = buildSelectQuery({
                table: 'orders',
                fields: ['status', 'COUNT(*)'],
                groupBy: 'status',
            });

            expect(sql).toBe('select status,COUNT(*) from orders  group by status');
        });

        it('should build query with array groupBy', () => {
            const sql = buildSelectQuery({
                table: 'orders',
                fields: ['status', 'user_id', 'COUNT(*)'],
                groupBy: ['status', 'user_id'],
            });

            expect(sql).toBe('select status,user_id,COUNT(*) from orders  group by status,user_id');
        });

        it('should build query with orderBy', () => {
            const sql = buildSelectQuery({
                table: 'users',
                orderBy: 'created_at',
                order: 'asc',
            });

            expect(sql).toBe('select * from users  order by created_at asc');
        });

        it('should build query with limit', () => {
            const sql = buildSelectQuery({
                table: 'users',
                limit: 10,
            });

            expect(sql).toBe('select * from users  limit 10');
        });

        it('should build query with offset', () => {
            const sql = buildSelectQuery({
                table: 'users',
                limit: 10,
                offset: 20,
            });

            expect(sql).toBe('select * from users  limit 10 offset 20');
        });

        it('should build complex query with all options', () => {
            const sql = buildSelectQuery({
                table: 'orders',
                fields: ['id', 'status', 'user_id', 'created_at'],
                conditions: [['status', 'completed'], ['user_id', '123']],
                groupBy: 'status',
                orderBy: 'created_at',
                order: 'desc',
                limit: 5,
                offset: 10,
            });

            expect(sql).toContain('select id,status,user_id,created_at from orders');
            expect(sql).toContain('where status = completed and user_id = 123');
            expect(sql).toContain('group by status');
            expect(sql).toContain('order by created_at desc');
            expect(sql).toContain('limit 5');
            expect(sql).toContain('offset 10');
        });
    });

    describe('buildUpdateQuery', () => {
        it('should build basic update query', () => {
            const sql = buildUpdateQuery({
                table: 'users',
                fields: [['name', 'John'], ['email', '<EMAIL>']],
            });

            expect(sql).toBe('update users set  name = John, email = <EMAIL>');
        });

        it('should build update query with conditions', () => {
            const sql = buildUpdateQuery({
                table: 'users',
                fields: [['status', 'inactive']],
                conditions: [['id', '123']],
            });

            expect(sql).toBe('update users set  status = inactive where id = 123');
        });

        it('should build update query with multiple conditions', () => {
            const sql = buildUpdateQuery({
                table: 'users',
                fields: [['last_login', 'NOW()']],
                conditions: [['id', '123'], ['status', 'active']],
            });

            expect(sql).toBe('update users set  last_login = NOW() where id = 123 and status = active');
        });
    });

    describe('buildDeleteQuery', () => {
        it('should build basic delete query', () => {
            const sql = buildDeleteQuery({
                table: 'users',
            });

            expect(sql).toBe('DELETE from users ');
        });

        it('should build delete query with conditions', () => {
            const sql = buildDeleteQuery({
                table: 'users',
                conditions: [['id', '123']],
            });

            expect(sql).toBe('DELETE from users where id = 123');
        });

        it('should build delete query with multiple conditions', () => {
            const sql = buildDeleteQuery({
                table: 'users',
                conditions: [['status', 'inactive'], ['last_login', 'NULL']],
            });

            expect(sql).toBe('DELETE from users where status = inactive and last_login = NULL');
        });
    });

    describe('buildInsertQuery', () => {
        it('should build insert query', () => {
            const sql = buildInsertQuery('users');
            expect(sql).toBe('INSERT INTO users SET ?');
        });
    });

});