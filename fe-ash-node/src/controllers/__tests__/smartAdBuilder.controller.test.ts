import {describe, it, expect, vi, beforeEach} from 'vitest';
import request from 'supertest';
import Koa from 'koa';
import bodyParser from 'koa-bodyparser';
import smartAdBuilderRouter from '../smartAdBuilder.controller';
import * as model from '../../models/smartAdBuilder.model';
import {ERROR_CODE} from '../../config/errorCode';

// Mock the database module before importing anything that uses it
vi.mock('../../utils/db', () => ({
    dbQuery: vi.fn(),
    buildSelectQuery: vi.fn(),
}));

vi.mock('../../models/smartAdBuilder.model');

describe('SmartAdBuilder Controller', () => {
    let app: Koa = new Koa();

    beforeEach(() => {
        app = new Koa();

        app.context.responseSuccess = vi.fn(function (this: Koa.Context, data: any = null) {
            this.status = 200;
            this.body = {
                status: 0,
                data,
                errors: [],
            };
        });

        app.context.responseFailure = vi.fn(function (this: Koa.Context, data: any = null, errors: any[] = []) {
            this.status = 200;
            this.body = {
                status: 2,
                data,
                errors,
            };
        });

        app.use(bodyParser());

        app.use(async (ctx, next) => {
            const {userid, params} = ctx.request.body as any;
            ctx.state.userid = userid;
            ctx.state.params = JSON.parse(params);
            await next();
        });

        app.use(smartAdBuilderRouter.routes()).use(smartAdBuilderRouter.allowedMethods());
    });

    describe('POST /ash-api/SmartAdBuilder/getFieldsTemplateByTrade', () => {
        it('should return failure when trade1 is missing', async () => {
            const response = await request(app.callback())
                .post('/ash-api/SmartAdBuilder/getFieldsTemplateByTrade')
                .send({
                    userid: 'test_user',
                    params: JSON.stringify({}),
                })
                .expect(200);

            expect(response.body).toEqual({
                status: 2,
                data: [],
                errors: [ERROR_CODE.getFieldsTemplateByTrade_invalidParams],
            });
        });

        it('should return success with template data when trade1 is provided', async () => {
            const mockTemplate = {
                businessTemplate: 'test business template',
                productSummaryTemplate: 'test product summary template',
            };

            vi.mocked(model.getFieldsTemplateByTrade).mockResolvedValue(mockTemplate);

            const response = await request(app.callback())
                .post('/ash-api/SmartAdBuilder/getFieldsTemplateByTrade')
                .send({
                    userid: 'test_user',
                    params: JSON.stringify({trade1: 123}),
                })
                .expect(200);

            expect(response.body).toEqual({
                status: 0,
                data: mockTemplate,
                errors: [],
            });

            expect(model.getFieldsTemplateByTrade).toHaveBeenCalledWith({
                trade1: 123,
                trade2: undefined,
            });
        });

        it('should return success with template data when both trade1 and trade2 are provided', async () => {
            const mockTemplate = {
                businessTemplate: 'test business template',
                productSummaryTemplate: 'test product summary template',
            };

            vi.mocked(model.getFieldsTemplateByTrade).mockResolvedValue(mockTemplate);

            const response = await request(app.callback())
                .post('/ash-api/SmartAdBuilder/getFieldsTemplateByTrade')
                .send({
                    userid: 'test_user',
                    params: JSON.stringify({trade1: 123, trade2: 456}),
                })
                .expect(200);

            expect(response.body).toEqual({
                status: 0,
                data: mockTemplate,
                errors: [],
            });

            expect(model.getFieldsTemplateByTrade).toHaveBeenCalledWith({
                trade1: 123,
                trade2: 456,
            });
        });

        it('should return filtered fields when fields parameter is provided', async () => {
            const mockTemplate = {
                businessTemplate: 'test business template',
                productSummaryTemplate: 'test product summary template',
            };

            vi.mocked(model.getFieldsTemplateByTrade).mockResolvedValue(mockTemplate);

            const response = await request(app.callback())
                .post('/ash-api/SmartAdBuilder/getFieldsTemplateByTrade')
                .send({
                    userid: 'test_user',
                    params: JSON.stringify({
                        trade1: 123,
                        fields: ['businessTemplate'],
                    }),
                })
                .expect(200);

            expect(response.body).toEqual({
                status: 0,
                data: {businessTemplate: 'test business template'},
                errors: [],
            });
        });

        it('should return failure when database query fails', async () => {
            vi.mocked(model.getFieldsTemplateByTrade).mockRejectedValue(new Error('Database error'));

            const response = await request(app.callback())
                .post('/ash-api/SmartAdBuilder/getFieldsTemplateByTrade')
                .send({
                    userid: 'test_user',
                    params: JSON.stringify({trade1: 123}),
                })
                .expect(200);

            expect(response.body).toEqual({
                status: 2,
                data: [],
                errors: [ERROR_CODE.getFieldsTemplateByTrade_sqlError],
            });
        });
    });
});
