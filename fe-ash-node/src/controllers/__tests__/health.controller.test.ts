import {describe, it, expect, vi, beforeEach} from 'vitest';
import request from 'supertest';
import Koa from 'koa';
import healthRouter from '../health.controller';

describe('Health Controller', () => {
    let app: Koa = new Koa();

    beforeEach(() => {
        app = new Koa();

        app.context.responseSuccess = vi.fn(function (this: Koa.Context, data: any = null) {
            this.status = 200;
            this.body = {
                status: 0,
                data,
                errors: [],
            };
        });

        app.use(healthRouter.routes()).use(healthRouter.allowedMethods());
    });

    describe('GET /health', () => {
        it('should return success response', async () => {
            const response = await request(app.callback())
                .get('/health')
                .expect(200);

            expect(response.body).toEqual({
                status: 0,
                data: null,
                errors: [],
            });
        });
    });
});
