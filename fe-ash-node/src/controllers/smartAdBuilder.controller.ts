import Router from '@koa/router';
import {pick} from 'lodash';
import {HairuoRequestBody} from '../interface/request';
import {ERROR_CODE} from '../config/errorCode';
import {getFieldsTemplateByTrade} from '../models/smartAdBuilder.model';

const router = new Router({prefix: '/ash-api/SmartAdBuilder'});

interface GetFieldsTemplateByTradeParams {
    trade1: number;
    trade2: number;
    fields?: string[];
}
router.post('/getFieldsTemplateByTrade', async ctx => {
    const {params} = ctx.state as HairuoRequestBody;
    const {trade1, trade2, fields} = params as GetFieldsTemplateByTradeParams;

    if (!trade1) {
        ctx.responseFailure(
            [],
            [ERROR_CODE.getFieldsTemplateByTrade_invalidParams]
        );
        return;
    }

    try {
        const template = await getFieldsTemplateByTrade({trade1, trade2});
        const result = fields?.length ? pick(template, fields) : template;
        ctx.responseSuccess(result);
    }
    catch (e) {
        ctx.responseFailure(
            [],
            [ERROR_CODE.getFieldsTemplateByTrade_sqlError]
        );
    }
});

export default router;
