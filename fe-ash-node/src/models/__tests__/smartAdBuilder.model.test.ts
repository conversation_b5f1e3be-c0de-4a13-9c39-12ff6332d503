import {describe, it, expect, vi, beforeEach} from 'vitest';
import {getFieldsTemplateByTrade} from '../smartAdBuilder.model';
import * as db from '../../utils/db';

// Mock the database module before importing anything that uses it
vi.mock('../../utils/db', () => ({
    dbQuery: vi.fn(),
    buildSelectQuery: vi.fn(),
}));

describe('SmartAdBuilder Model', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('getFieldsTemplateByTrade', () => {
        it('should build correct SQL query with only trade1', async () => {
            const mockResult = {
                businessTemplate: 'test business template',
                productSummaryTemplate: 'test product summary template',
            };

            vi.mocked(db.buildSelectQuery)
                .mockReturnValue('SELECT * FROM smart_build_trade_templates WHERE trade1_id = 123');
            vi.mocked(db.dbQuery).mockResolvedValue([mockResult]);

            const result = await getFieldsTemplateByTrade({trade1: 123});

            expect(db.buildSelectQuery).toHaveBeenCalledWith({
                table: 'smart_build_trade_templates',
                fields: ['business_template as businessTemplate', 'product_summary_template as productSummaryTemplate'],
                conditions: [['trade1_id', 123]],
            });

            expect(result).toEqual(mockResult);
        });

        it('should build correct SQL query with both trade1 and trade2', async () => {
            const mockResult = {
                businessTemplate: 'test business template',
                productSummaryTemplate: 'test product summary template',
            };

            vi.mocked(db.buildSelectQuery)
                .mockReturnValue('SELECT * FROM smart_build_trade_templates WHERE trade1_id = 123 AND trade2_id = 456');
            vi.mocked(db.dbQuery).mockResolvedValue([mockResult]);

            const result = await getFieldsTemplateByTrade({trade1: 123, trade2: 456});

            expect(db.buildSelectQuery).toHaveBeenCalledWith({
                table: 'smart_build_trade_templates',
                fields: ['business_template as businessTemplate', 'product_summary_template as productSummaryTemplate'],
                conditions: [['trade1_id', 123], ['trade2_id', 456]],
            });

            expect(result).toEqual(mockResult);
        });

        it('should handle database query error', async () => {
            const error = new Error('Database connection error');
            vi.mocked(db.buildSelectQuery).mockReturnValue('SELECT * FROM smart_build_trade_templates');
            vi.mocked(db.dbQuery).mockRejectedValue(error);

            await expect(getFieldsTemplateByTrade({trade1: 123})).rejects.toThrow('Database connection error');
        });

        it('should return undefined when no results found', async () => {
            vi.mocked(db.buildSelectQuery)
                .mockReturnValue('SELECT * FROM smart_build_trade_templates WHERE trade1_id = 999');
            vi.mocked(db.dbQuery).mockResolvedValue([]);

            const result = await getFieldsTemplateByTrade({trade1: 999});

            expect(result).toBeUndefined();
        });
    });
});
