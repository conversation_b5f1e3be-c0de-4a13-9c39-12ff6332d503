import {dbQuery, buildSelectQuery} from '../utils/db';

export interface FieldTemplate {
    trade1: number;
    trade2?: number;
    business_template?: string;
    product_summary_template?: string;
}

export async function getFieldsTemplateByTrade({trade1, trade2}: Pick<FieldTemplate, 'trade1' | 'trade2'>) {
    const sql = trade2
        ? buildSelectQuery({
            table: 'smart_build_trade_templates',
            fields: ['business_template as businessTemplate', 'product_summary_template as productSummaryTemplate'],
            conditions: [['trade1_id', trade1], ['trade2_id', trade2]],
        })
        : buildSelectQuery({
            table: 'smart_build_trade_templates',
            fields: ['business_template as businessTemplate', 'product_summary_template as productSummaryTemplate'],
            conditions: [['trade1_id', trade1]],
        });

    const [result] = await dbQuery(sql);

    return result;
}
