import {describe, it, expect, vi, beforeEach, type Mock} from 'vitest';
import {error} from '../error';
import {Context} from 'koa';

describe('Error Middleware', () => {
    let ctx: Partial<Context> = {};
    let next: Mock = vi.fn();

    beforeEach(() => {
        ctx = {
            internalServerError: vi.fn(),
            app: {
                emit: vi.fn(),
            } as any,
        };
        next = vi.fn();
    });

    it('should call next when no error occurs', async () => {
        await error(ctx as Context, next);

        expect(next).toHaveBeenCalled();
        expect(ctx.internalServerError).not.toHaveBeenCalled();
        expect(ctx.app!.emit).not.toHaveBeenCalled();
    });

    it('should handle errors and call internalServerError', async () => {
        const testError = new Error('Test error');
        next.mockRejectedValue(testError);

        await error(ctx as Context, next);

        expect(next).toHaveBeenCalled();
        expect(ctx.internalServerError).toHaveBeenCalledWith(testError, ctx);
        expect(ctx.app!.emit).toHaveBeenCalledWith('error', testError, ctx);
    });

    it('should handle non-Error objects', async () => {
        const testError = 'String error';
        next.mockRejectedValue(testError);

        await error(ctx as Context, next);

        expect(ctx.internalServerError).toHaveBeenCalledWith(testError, ctx);
        expect(ctx.app!.emit).toHaveBeenCalledWith('error', testError, ctx);
    });
});