import {describe, it, expect, vi, beforeEach, type Mock, type SpyInstance} from 'vitest';
import {logger} from '../logger';
import {Context} from 'koa';

describe('Logger Middleware', () => {
    let ctx: Partial<Context> = {};
    let next: Mock = vi.fn();
    let consoleSpy: SpyInstance = vi.fn();

    beforeEach(() => {
        ctx = {
            method: 'GET',
            url: '/test',
        };
        next = vi.fn();
        consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    });

    it('should log request method, url and duration', async () => {
        const mockStart = Date.now();
        vi.spyOn(Date, 'now')
            .mockReturnValueOnce(mockStart)
            .mockReturnValueOnce(mockStart + 150);

        await logger(ctx as Context, next);

        expect(next).toHaveBeenCalled();
        expect(consoleSpy).toHaveBeenCalledWith('GET /test - 150ms');
    });

    it('should handle different HTTP methods and URLs', async () => {
        ctx.method = 'POST';
        ctx.url = '/api/users';

        const mockStart = Date.now();
        vi.spyOn(Date, 'now')
            .mockReturnValueOnce(mockStart)
            .mockReturnValueOnce(mockStart + 75);

        await logger(ctx as Context, next);

        expect(consoleSpy).toHaveBeenCalledWith('POST /api/users - 75ms');
    });

    it('should not log if next throws an error', async () => {
        const error = new Error('Test error');
        next.mockRejectedValue(error);

        const mockStart = 1000;
        vi.spyOn(Date, 'now').mockReturnValue(mockStart);

        await expect(logger(ctx as Context, next)).rejects.toThrow('Test error');
        expect(consoleSpy).not.toHaveBeenCalled();
    });
});
