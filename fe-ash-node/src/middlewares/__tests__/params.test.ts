import {describe, it, expect, vi, beforeEach, type Mock} from 'vitest';
import {paramsMiddleware} from '../params';
import {Context} from 'koa';

describe('Params Middleware', () => {
    let ctx: Partial<Context> = {};
    let next: Mock = vi.fn();

    beforeEach(() => {
        ctx = {
            request: {
                body: {},
            } as any,
            state: {},
        };
        next = vi.fn();
    });

    it('should parse request body and set state', async () => {
        const requestBody = {
            userid: 'test_user',
            params: JSON.stringify({trade1: 123, trade2: 456}),
        };

        ctx.request!.body = requestBody;

        await paramsMiddleware(ctx as Context, next);

        expect(ctx.state!.userid).toBe('test_user');
        expect(ctx.state!.params).toEqual({trade1: 123, trade2: 456});
        expect(next).toHaveBeenCalled();
    });

    it('should handle complex params object', async () => {
        const complexParams = {
            nested: {
                field: 'value',
                array: [1, 2, 3],
            },
            boolean: true,
            number: 42,
        };

        const requestBody = {
            userid: 'complex_user',
            params: JSON.stringify(complexParams),
        };

        ctx.request!.body = requestBody;

        await paramsMiddleware(ctx as Context, next);

        expect(ctx.state!.userid).toBe('complex_user');
        expect(ctx.state!.params).toEqual(complexParams);
    });

    it('should handle empty params', async () => {
        const requestBody = {
            userid: 'empty_user',
            params: '{}',
        };

        ctx.request!.body = requestBody;

        await paramsMiddleware(ctx as Context, next);

        expect(ctx.state!.userid).toBe('empty_user');
        expect(ctx.state!.params).toEqual({});
    });

    it('should throw error for invalid JSON params', async () => {
        const requestBody = {
            userid: 'invalid_user',
            params: 'invalid json',
        };

        ctx.request!.body = requestBody;

        await expect(paramsMiddleware(ctx as Context, next)).rejects.toThrow();
        expect(next).not.toHaveBeenCalled();
    });
});
