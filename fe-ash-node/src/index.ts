import Koa from 'koa';
import bodyParser from 'koa-bodyparser';
import json from 'koa-json';
import {logger} from './middlewares/logger';
import {error} from './middlewares/error';
import {paramsMiddleware} from './middlewares/params';
import smartAdBuilderRouter from './controllers/smartAdBuilder.controller';
import healthRouter from './controllers/health.controller';
import * as env from './utils/env';

const app = new Koa();

// 中间件
app.use(error);
app.use(logger);
app.use(bodyParser());
app.use(paramsMiddleware);
app.use(json());

app.context.responseSuccess = function(data: any = null) {
    this.status = 200;
    this.body = {
        status: 0,
        data,
        errors: [],
    };
};

app.context.responseFailure = function(data: any = null, errors: any[] = []) {
    this.status = 200;
    this.body = {
        status: 2,
        data,
        errors,
    };
};

app.context.notFound = function(err: any) {
    this.status = 404;
    this.body = {
        error: {
            message: 'Not Found',
            details: process.env.NODE_ENV === 'development' ? err.stack : undefined
        }
    };
};

app.context.internalServerError = function(err: any) {
    this.status = 500;
    this.body = {
        error: {
            message: 'Internal Server Error',
            details: process.env.NODE_ENV === 'development' ? err.stack : undefined
        }
    };
};

// 路由
app.use(smartAdBuilderRouter.routes()).use(smartAdBuilderRouter.allowedMethods());
app.use(healthRouter.routes()).use(healthRouter.allowedMethods());

// 启动服务器
const address = (env.args as any).address;
const port = (env.args as any).port;
app.listen(port, address, () => {
    console.log(`Server running on http://localhost:${port}`);
});