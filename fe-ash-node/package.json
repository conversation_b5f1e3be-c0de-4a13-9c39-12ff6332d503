{"name": "fe-ash-node", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc -p tsconfig.build.json", "start": "node dist/index.js", "dev": "set NODE_ENV=development && nodemon --exec ./node_modules/.bin/ts-node -- src/index.ts"}, "dependencies": {"@baidu/node-baikaldb": "0.0.18", "@koa/router": "^12.0.0", "config": "^3.3.9", "koa": "^2.14.2", "koa-bodyparser": "^4.3.0", "koa-json": "^2.0.2", "lodash": "^4.17.21", "pm2": "^6.0.8", "yargs": "^12.0.5"}, "devDependencies": {"@types/config": "^3.3.5", "@types/koa": "^2.13.7", "@types/koa__router": "^12.0.1", "@types/koa-bodyparser": "^4.3.12", "@types/koa-json": "^2.0.23", "@types/lodash": "^4.17.20", "@types/node": "^20.8.9", "@types/yargs": "^17.0.33", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}