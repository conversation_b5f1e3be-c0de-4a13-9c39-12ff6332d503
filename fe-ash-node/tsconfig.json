{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "outDir": "./dist",
    "rootDir": ".",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "composite": true,
    "baseUrl": ".",
    "paths": {
      "@shared/*": ["../fe-ash-shared/src/*"]
    }
  },
  "include": ["src/**/*", "src/interface/global.d.ts", "vitest.config.ts"],
  "exclude": ["node_modules"],
}