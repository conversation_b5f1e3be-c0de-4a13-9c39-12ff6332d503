{"name": "fe-ash-monorepo", "version": "1.0.0", "description": "Frontend ASH Monorepo - Node.js services and crawling utilities", "private": true, "type": "module", "workspaces": ["fe-ash-shared", "fe-ash-node", "fe-ash-crawl"], "scripts": {"dev": "concurrently \"pnpm run dev:node\" \"pnpm run dev:crawl\"", "dev:node": "cd fe-ash-node && pnpm run dev", "dev:crawl": "cd fe-ash-crawl && pnpm run dev", "build": "pnpm run build:node && pnpm run build:crawl", "build:node": "pnpm run build --workspace=fe-ash-node", "build:crawl": "pnpm run build --workspace=fe-ash-crawl", "test:run": "vitest run", "test:node": "vitest run fe-ash-node", "test:crawl": "vitest run fe-ash-crawl", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "lint:node": "eslint fe-ash-node/src --ext .ts,.js", "lint:crawl": "eslint fe-ash-crawl/src --ext .ts,.js", "clean": "pnpm run clean:node && pnpm run clean:crawl", "clean:node": "cd fe-ash-node && rm -rf dist node_modules", "clean:crawl": "cd fe-ash-crawl && rm -rf dist node_modules"}, "devDependencies": {"@ecomfe/eslint-config": "^7.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@types/supertest": "^6.0.2", "concurrently": "^8.2.0", "eslint": "^8.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "supertest": "^6.3.4", "typescript": "^5.2.2", "vitest": "^1.2.0"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "git add"]}}