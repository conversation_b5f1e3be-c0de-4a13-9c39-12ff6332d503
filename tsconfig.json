{"compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "declaration": true, "composite": true, "baseUrl": ".", "paths": {"@shared/*": ["./fe-ash-shared/src/*"]}, "resolveJsonModule": true}, "references": [{"path": "./fe-ash-shared"}, {"path": "./fe-ash-node"}, {"path": "./fe-ash-crawl"}], "include": ["vitest.config.ts"], "exclude": ["node_modules", "dist", "output"]}